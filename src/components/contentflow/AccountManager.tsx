'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  Plus,
  Settings,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Users,
  TrendingUp,
  Calendar,
  BarChart3,
  Loader2
} from 'lucide-react';

interface AccountsMessages {
  title: string;
  subtitle: string;
  addAccount: string;
  refreshAll: string;
  platform: string;
  username: string;
  status: string;
  lastSync: string;
  health: string;
  autoPost: string;
  followers: string;
  following: string;
  posts: string;
  reach: string;
  engagement: string;
  growth: string;
  active: string;
  warning: string;
  error: string;
  disconnected: string;
  connected: string;
  connecting: string;
  syncNow: string;
  settings: string;
  disconnect: string;
  reconnect: string;
  accountHealth: string;
  performanceMetrics: string;
  quickActions: string;
  accountSynced: string;
  accountDisconnected: string;
  accountReconnected: string;
  autoPostEnabled: string;
  autoPostDisabled: string;
  minutes: string;
  hours: string;
  days: string;
}

interface SocialAccount {
  id: number;
  platform: string;
  username: string | null;
  displayName: string | null;
  followers: number | null;
  following: number | null;
  posts: number | null;
  status: string;
  lastSync: Date | null;
  health: number | null;
  autoPost: boolean;
  avatarUrl: string | null;
  createdAt: Date;
}

interface ContentFlowAccountsProps {
  accounts: AccountsMessages;
}

export const ContentFlowAccounts = ({ accounts: t }: ContentFlowAccountsProps) => {
  const { data: session } = useSession();
  const [accounts, setAccounts] = useState<SocialAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState<number | null>(null);
  const [connecting, setConnecting] = useState(false);

  // Fetch user's social accounts
  const fetchAccounts = async () => {
    console.log('🔄 AccountManager - Fetching social accounts');
    try {
      const response = await fetch('/api/social-accounts');
      if (response.ok) {
        const data = await response.json();
        console.log('✅ AccountManager - Accounts fetched:', data.data?.length || 0);
        setAccounts(data.data || []);
      } else {
        console.log('❌ AccountManager - Failed to fetch accounts');
        toast.error('Failed to load accounts');
      }
    } catch (error) {
      console.error('💥 AccountManager - Error fetching accounts:', error);
      toast.error('Error loading accounts');
    } finally {
      setLoading(false);
    }
  };

  // Load accounts on component mount
  useEffect(() => {
    if (session?.user) {
      fetchAccounts();
    } else {
      setLoading(false);
    }
  }, [session]);

  // Handle URL parameters for OAuth callbacks
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const error = urlParams.get('error');

    if (success === 'instagram_connected') {
      console.log('✅ AccountManager - Instagram connected successfully');
      toast.success('Instagram account connected successfully!');
      fetchAccounts(); // Refresh accounts list
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (error) {
      console.log('❌ AccountManager - OAuth error:', error);
      const errorMessages: { [key: string]: string } = {
        'instagram_oauth_denied': 'Instagram authorization was denied',
        'instagram_oauth_invalid': 'Invalid Instagram authorization',
        'instagram_oauth_invalid_state': 'Invalid authorization state',
        'instagram_oauth_unauthorized': 'Unauthorized access',
        'instagram_token_exchange_failed': 'Failed to exchange Instagram token',
        'instagram_profile_fetch_failed': 'Failed to fetch Instagram profile',
        'user_not_found': 'User not found',
        'instagram_oauth_error': 'Instagram authorization error'
      };
      toast.error(errorMessages[error] || 'Authorization failed');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const availablePlatforms = [
    { name: "Instagram", icon: Instagram, color: "bg-pink-500", connected: false },
    { name: "YouTube", icon: Youtube, color: "bg-red-600", connected: false },
    { name: "Facebook", icon: Users, color: "bg-blue-700", connected: false },
    { name: "Pinterest", icon: BarChart3, color: "bg-red-700", connected: false },
    { name: "小红书", icon: TrendingUp, color: "bg-red-400", connected: false }
  ];

  // Handle account sync
  const handleSync = async (accountId: number) => {
    console.log(`🔄 AccountManager - Syncing account ID: ${accountId}`);
    setSyncing(accountId);
    
    try {
      const response = await fetch(`/api/social-accounts/${accountId}/sync`, {
        method: 'POST'
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ AccountManager - Sync successful:', data);
        toast.success(t.accountSynced || 'Account synchronized successfully');
        await fetchAccounts(); // Refresh accounts list
      } else {
        const errorData = await response.json();
        console.log('❌ AccountManager - Sync failed:', errorData);
        toast.error(errorData.error || 'Failed to sync account');
      }
    } catch (error) {
      console.error('💥 AccountManager - Sync error:', error);
      toast.error('Error syncing account');
    } finally {
      setSyncing(null);
    }
  };

  // Handle auto-post toggle
  const handleToggleAutoPost = async (accountId: number) => {
    console.log(`🔄 AccountManager - Toggling auto-post for account ID: ${accountId}`);
    
    // Optimistically update UI
    setAccounts(accounts.map(account => 
      account.id === accountId 
        ? { ...account, autoPost: !account.autoPost }
        : account
    ));
    
    // TODO: Implement API call to update auto-post setting
    toast.success(t.autoPostEnabled || 'Auto-post setting updated');
  };

  // Handle platform connection
  const handleConnectPlatform = async (platformName: string) => {
    console.log(`🔗 AccountManager - Connecting to ${platformName}`);
    
    if (platformName === 'Instagram') {
      setConnecting(true);
      try {
        // Redirect to Instagram OAuth
        window.location.href = '/api/auth/instagram/authorize';
      } catch (error) {
        console.error('💥 AccountManager - Instagram connection error:', error);
        toast.error('Failed to connect to Instagram');
        setConnecting(false);
      }
    } else {
      toast.info(`${platformName} integration coming soon!`);
    }
  };

  // Handle account disconnect
  const handleDisconnect = async (accountId: number) => {
    console.log(`🔌 AccountManager - Disconnecting account ID: ${accountId}`);
    
    try {
      const response = await fetch(`/api/social-accounts/${accountId}/disconnect`, {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log('✅ AccountManager - Disconnect successful');
        toast.success(t.accountDisconnected || 'Account disconnected successfully');
        await fetchAccounts(); // Refresh accounts list
      } else {
        const errorData = await response.json();
        console.log('❌ AccountManager - Disconnect failed:', errorData);
        toast.error(errorData.error || 'Failed to disconnect account');
      }
    } catch (error) {
      console.error('💥 AccountManager - Disconnect error:', error);
      toast.error('Error disconnecting account');
    }
  };

  // Get platform icon and color
  const getPlatformConfig = (platform: string) => {
    const configs: { [key: string]: { icon: any; color: string } } = {
      instagram: { icon: Instagram, color: 'bg-pink-500' },
      tiktok: { icon: Youtube, color: 'bg-red-500' },
      linkedin: { icon: Linkedin, color: 'bg-blue-600' },
      twitter: { icon: Twitter, color: 'bg-blue-400' },
      youtube: { icon: Youtube, color: 'bg-red-600' },
      facebook: { icon: Users, color: 'bg-blue-700' }
    };
    return configs[platform.toLowerCase()] || { icon: Users, color: 'bg-gray-500' };
  };

  // Format last sync time
  const formatLastSync = (lastSync: Date | null) => {
    if (!lastSync) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - lastSync.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} ${t.minutes || 'minutes'} ago`;
    if (diffHours < 24) return `${diffHours} ${t.hours || 'hours'} ago`;
    return `${diffDays} ${t.days || 'days'} ago`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'inactive': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'inactive': return <XCircle className="w-4 h-4" />;
      default: return <XCircle className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{t.title || 'Account Management'}</h1>
          <p className="text-gray-600 mt-1">{t.subtitle || 'Manage your social media account connections'}</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            {t.refreshAll || 'Sync All'}
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            {t.addAccount || 'Connect Account'}
          </Button>
        </div>
      </div>

      {/* Connected Accounts */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">Connected Accounts</h2>
        
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-600">Loading accounts...</span>
          </div>
        ) : accounts.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No accounts connected</h3>
            <p className="text-gray-600 mb-4">Connect your social media accounts to get started</p>
            <Button onClick={() => handleConnectPlatform('Instagram')} disabled={connecting}>
              {connecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Connect Instagram
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {accounts.map((account) => {
              const platformConfig = getPlatformConfig(account.platform);
              const IconComponent = platformConfig.icon;
              
              return (
                <Card key={account.id} className="border-0 shadow-sm hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-lg ${platformConfig.color} text-white`}>
                          <IconComponent className="w-6 h-6" />
                        </div>
                        <div>
                          <CardTitle className="text-lg capitalize">{account.platform}</CardTitle>
                          <CardDescription>{account.username || account.displayName}</CardDescription>
                        </div>
                      </div>
                      <Badge className={`${getStatusColor(account.status)} text-xs`}>
                        {getStatusIcon(account.status)}
                        <span className="ml-1 capitalize">{account.status}</span>
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Account Stats */}
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-lg font-bold text-gray-900">
                          {account.followers?.toLocaleString() || '0'}
                        </p>
                        <p className="text-xs text-gray-600">{t.followers || 'Followers'}</p>
                      </div>
                      <div>
                        <p className="text-lg font-bold text-gray-900">
                          {account.following?.toLocaleString() || '0'}
                        </p>
                        <p className="text-xs text-gray-600">{t.following || 'Following'}</p>
                      </div>
                      <div>
                        <p className="text-lg font-bold text-gray-900">
                          {account.posts?.toLocaleString() || '0'}
                        </p>
                        <p className="text-xs text-gray-600">{t.posts || 'Posts'}</p>
                      </div>
                    </div>

                    {/* Last Sync */}
                    <div className="text-center py-2 bg-gray-50 rounded">
                      <p className="text-sm text-gray-600">
                        {t.lastSync || 'Last sync'}: {formatLastSync(account.lastSync)}
                      </p>
                    </div>

                    {/* Health */}
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">{t.health || 'Health'}</span>
                        <span className="text-sm font-medium">{account.health || 0}%</span>
                      </div>
                      <Progress value={account.health || 0} className="h-2" />
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-2 border-t">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={account.autoPost}
                          onCheckedChange={() => handleToggleAutoPost(account.id)}
                        />
                        <span className="text-sm text-gray-600">{t.autoPost || 'Auto-post'}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleSync(account.id)}
                          disabled={syncing === account.id}
                        >
                          {syncing === account.id ? (
                            <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                          ) : (
                            <RefreshCw className="w-3 h-3 mr-1" />
                          )}
                          {t.syncNow || 'Sync'}
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDisconnect(account.id)}
                        >
                          <XCircle className="w-3 h-3 mr-1" />
                          {t.disconnect || 'Disconnect'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};
