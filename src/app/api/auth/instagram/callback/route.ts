import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';
import { prisma } from '@/lib/prisma';

/**
 * Instagram OAuth Callback Endpoint
 * Handles the callback from Instagram OAuth and exchanges code for access token
 */
export async function GET(request: NextRequest) {
  console.log('🔄 Instagram OAuth Callback - Processing callback');
  
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');

    console.log('📥 Instagram OAuth Callback - Received parameters:', {
      hasCode: !!code,
      hasState: !!state,
      error: error
    });

    // Handle OAuth error
    if (error) {
      console.log('❌ Instagram OAuth Callback - OAuth error:', error);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_oauth_denied`
      );
    }

    // Validate required parameters
    if (!code || !state) {
      console.log('❌ Instagram OAuth Callback - Missing required parameters');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_oauth_invalid`
      );
    }

    // Verify state parameter
    let stateData;
    try {
      stateData = JSON.parse(Buffer.from(state, 'base64').toString());
      console.log('🔐 Instagram OAuth Callback - State verified for user:', stateData.userId);
    } catch (error) {
      console.log('❌ Instagram OAuth Callback - Invalid state parameter');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_oauth_invalid_state`
      );
    }

    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user || session.user.id !== stateData.userId) {
      console.log('❌ Instagram OAuth Callback - User authentication mismatch');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_oauth_unauthorized`
      );
    }

    console.log('✅ Instagram OAuth Callback - User authenticated:', session.user.email);

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://api.instagram.com/oauth/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.INSTAGRAM_CLIENT_ID!,
        client_secret: process.env.INSTAGRAM_CLIENT_SECRET!,
        grant_type: 'authorization_code',
        redirect_uri: process.env.INSTAGRAM_REDIRECT_URI!,
        code: code,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.log('❌ Instagram OAuth Callback - Token exchange failed:', errorData);
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_token_exchange_failed`
      );
    }

    const tokenData = await tokenResponse.json();
    console.log('🎯 Instagram OAuth Callback - Token exchange successful');
    console.log('📊 Instagram OAuth Callback - Token data:', {
      hasAccessToken: !!tokenData.access_token,
      userId: tokenData.user_id
    });

    // Get long-lived access token
    const longLivedTokenResponse = await fetch(
      `https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=${process.env.INSTAGRAM_CLIENT_SECRET}&access_token=${tokenData.access_token}`
    );

    let finalAccessToken = tokenData.access_token;
    let expiresAt = null;

    if (longLivedTokenResponse.ok) {
      const longLivedData = await longLivedTokenResponse.json();
      finalAccessToken = longLivedData.access_token;
      expiresAt = new Date(Date.now() + (longLivedData.expires_in * 1000));
      console.log('🔄 Instagram OAuth Callback - Long-lived token obtained, expires at:', expiresAt);
    } else {
      console.log('⚠️ Instagram OAuth Callback - Failed to get long-lived token, using short-lived');
    }

    // Get user profile information
    const profileResponse = await fetch(
      `https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${finalAccessToken}`
    );

    if (!profileResponse.ok) {
      console.log('❌ Instagram OAuth Callback - Failed to fetch profile');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_profile_fetch_failed`
      );
    }

    const profileData = await profileResponse.json();
    console.log('👤 Instagram OAuth Callback - Profile data retrieved:', {
      id: profileData.id,
      username: profileData.username,
      accountType: profileData.account_type,
      mediaCount: profileData.media_count
    });

    // Find user by UUID
    const user = await prisma.user.findUnique({
      where: { uuid: session.user.uuid }
    });

    if (!user) {
      console.log('❌ Instagram OAuth Callback - User not found in database');
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=user_not_found`
      );
    }

    // Save or update Instagram account in database
    const existingAccount = await prisma.socialAccount.findFirst({
      where: {
        userUuid: user.uuid,
        platform: 'instagram',
        accountId: profileData.id
      }
    });

    if (existingAccount) {
      // Update existing account
      await prisma.socialAccount.update({
        where: { id: existingAccount.id },
        data: {
          accessToken: finalAccessToken,
          expiresAt: expiresAt,
          username: profileData.username,
          posts: profileData.media_count,
          isActive: true,
          lastSync: new Date(),
          updatedAt: new Date()
        }
      });
      console.log('🔄 Instagram OAuth Callback - Updated existing Instagram account');
    } else {
      // Create new account
      await prisma.socialAccount.create({
        data: {
          userUuid: user.uuid,
          platform: 'instagram',
          accountId: profileData.id,
          username: profileData.username,
          displayName: profileData.username,
          accessToken: finalAccessToken,
          expiresAt: expiresAt,
          posts: profileData.media_count,
          isActive: true,
          lastSync: new Date()
        }
      });
      console.log('✨ Instagram OAuth Callback - Created new Instagram account');
    }

    console.log('🎉 Instagram OAuth Callback - Account linking completed successfully');

    // Redirect back to accounts page with success
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?success=instagram_connected`
    );

  } catch (error) {
    console.error('💥 Instagram OAuth Callback - Error:', error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_WEB_URL}/app/accounts?error=instagram_oauth_error`
    );
  }
}
