import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';

/**
 * Instagram OAuth Authorization Endpoint
 * Initiates the Instagram OAuth flow by redirecting to Instagram's authorization URL
 */
export async function GET(request: NextRequest) {
  console.log('🚀 Instagram OAuth Authorization - Starting flow');
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Instagram OAuth Authorization - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ Instagram OAuth Authorization - User authenticated:', session.user.email);

    // Get Instagram OAuth credentials from environment
    const clientId = process.env.INSTAGRAM_CLIENT_ID;
    const redirectUri = process.env.INSTAGRAM_REDIRECT_URI;

    if (!clientId || !redirectUri) {
      console.log('❌ Instagram OAuth Authorization - Missing environment variables');
      console.log('INSTAGRAM_CLIENT_ID:', !!clientId);
      console.log('INSTAGRAM_REDIRECT_URI:', !!redirectUri);
      return NextResponse.json(
        { error: 'Instagram OAuth not configured' },
        { status: 500 }
      );
    }

    // Generate state parameter for security
    const state = Buffer.from(JSON.stringify({
      userId: session.user.id,
      timestamp: Date.now()
    })).toString('base64');

    console.log('🔐 Instagram OAuth Authorization - Generated state parameter');

    // Instagram Basic Display API authorization URL
    const authUrl = new URL('https://api.instagram.com/oauth/authorize');
    authUrl.searchParams.set('client_id', clientId);
    authUrl.searchParams.set('redirect_uri', redirectUri);
    authUrl.searchParams.set('scope', 'user_profile,user_media');
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('state', state);

    console.log('🔗 Instagram OAuth Authorization - Redirecting to Instagram:', authUrl.toString());

    // Redirect to Instagram authorization page
    return NextResponse.redirect(authUrl.toString());

  } catch (error) {
    console.error('💥 Instagram OAuth Authorization - Error:', error);
    return NextResponse.json(
      { error: 'Failed to initiate Instagram authorization' },
      { status: 500 }
    );
  }
}
