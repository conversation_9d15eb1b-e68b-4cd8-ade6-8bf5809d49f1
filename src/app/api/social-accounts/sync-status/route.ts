import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';
import { syncScheduler } from '@/lib/services/SyncScheduler';

/**
 * Get sync status and statistics
 */
export async function GET(request: NextRequest) {
  console.log('📊 Sync Status API - Getting sync status');
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Sync Status API - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ Sync Status API - User authenticated:', session.user.email);

    // Get sync statistics
    const stats = syncScheduler.getSyncStats();
    const allJobs = syncScheduler.getAllJobs();

    console.log('📊 Sync Status API - Retrieved sync statistics:', stats);

    return NextResponse.json({
      success: true,
      data: {
        statistics: stats,
        recentJobs: allJobs.slice(-10), // Last 10 jobs
        isSchedulerRunning: true // TODO: Add actual scheduler status check
      }
    });

  } catch (error) {
    console.error('💥 Sync Status API - Error:', error);
    return NextResponse.json(
      { error: 'Failed to get sync status' },
      { status: 500 }
    );
  }
}

/**
 * Control sync scheduler (start/stop)
 */
export async function POST(request: NextRequest) {
  console.log('🎛️ Sync Status API - Controlling sync scheduler');
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Sync Status API - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    const { action, intervalMinutes } = await request.json();

    console.log(`🎛️ Sync Status API - Action: ${action}, Interval: ${intervalMinutes}`);

    switch (action) {
      case 'start':
        syncScheduler.start(intervalMinutes || 60);
        console.log('✅ Sync Status API - Scheduler started');
        return NextResponse.json({
          success: true,
          message: 'Sync scheduler started'
        });

      case 'stop':
        syncScheduler.stop();
        console.log('✅ Sync Status API - Scheduler stopped');
        return NextResponse.json({
          success: true,
          message: 'Sync scheduler stopped'
        });

      default:
        console.log('❌ Sync Status API - Invalid action');
        return NextResponse.json(
          { error: 'Invalid action. Use "start" or "stop"' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('💥 Sync Status API - Error:', error);
    return NextResponse.json(
      { error: 'Failed to control sync scheduler' },
      { status: 500 }
    );
  }
}
