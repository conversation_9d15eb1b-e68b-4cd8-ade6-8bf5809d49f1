import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';
import { prisma } from '@/lib/prisma';
import { InstagramService } from '@/lib/services/InstagramService';

/**
 * Sync all user's social media accounts
 */
export async function POST(request: NextRequest) {
  console.log('🔄 Sync All Accounts API - Starting bulk sync');
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Sync All Accounts API - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ Sync All Accounts API - User authenticated:', session.user.email);

    // Find user by UUID
    const user = await prisma.user.findUnique({
      where: { uuid: session.user.uuid }
    });

    if (!user) {
      console.log('❌ Sync All Accounts API - User not found in database');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get all active social accounts for the user
    const socialAccounts = await prisma.socialAccount.findMany({
      where: {
        userUuid: user.uuid,
        isActive: true,
        isDeleted: false
      }
    });

    console.log(`📊 Sync All Accounts API - Found ${socialAccounts.length} accounts to sync`);

    if (socialAccounts.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No accounts to sync',
        results: []
      });
    }

    // Sync each account
    const syncResults = [];
    const instagramService = new InstagramService();

    for (const account of socialAccounts) {
      console.log(`🔄 Sync All Accounts API - Syncing ${account.platform} account: ${account.username}`);
      
      try {
        let syncResult;
        
        switch (account.platform) {
          case 'instagram':
            syncResult = await instagramService.syncAccountData(account.id);
            break;
          default:
            console.log(`⚠️ Sync All Accounts API - Unsupported platform: ${account.platform}`);
            syncResult = {
              success: false,
              message: `Sync not supported for platform: ${account.platform}`,
              error: 'UNSUPPORTED_PLATFORM'
            };
            break;
        }

        syncResults.push({
          accountId: account.id,
          platform: account.platform,
          username: account.username,
          ...syncResult
        });

        console.log(`${syncResult.success ? '✅' : '❌'} Sync All Accounts API - ${account.platform} sync ${syncResult.success ? 'completed' : 'failed'}`);

      } catch (error) {
        console.error(`💥 Sync All Accounts API - Error syncing ${account.platform} account:`, error);
        syncResults.push({
          accountId: account.id,
          platform: account.platform,
          username: account.username,
          success: false,
          message: 'Sync failed due to error',
          error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
        });
      }
    }

    // Calculate summary
    const successCount = syncResults.filter(result => result.success).length;
    const failureCount = syncResults.length - successCount;

    console.log(`📊 Sync All Accounts API - Sync completed: ${successCount} successful, ${failureCount} failed`);

    return NextResponse.json({
      success: true,
      message: `Synced ${successCount} of ${syncResults.length} accounts`,
      summary: {
        total: syncResults.length,
        successful: successCount,
        failed: failureCount
      },
      results: syncResults
    });

  } catch (error) {
    console.error('💥 Sync All Accounts API - Error:', error);
    return NextResponse.json(
      { error: 'Failed to sync accounts' },
      { status: 500 }
    );
  }
}
