import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';
import { prisma } from '@/lib/prisma';
import { InstagramService } from '@/lib/services/InstagramService';

/**
 * Disconnect specific social media account
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log(`🔌 Social Account Disconnect API - Disconnecting account ID: ${params.id}`);
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Social Account Disconnect API - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ Social Account Disconnect API - User authenticated:', session.user.email);

    const accountId = parseInt(params.id);
    if (isNaN(accountId)) {
      console.log('❌ Social Account Disconnect API - Invalid account ID');
      return NextResponse.json(
        { error: 'Invalid account ID' },
        { status: 400 }
      );
    }

    // Find user by UUID
    const user = await prisma.user.findUnique({
      where: { uuid: session.user.uuid }
    });

    if (!user) {
      console.log('❌ Social Account Disconnect API - User not found in database');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Verify account belongs to user
    const account = await prisma.socialAccount.findFirst({
      where: {
        id: accountId,
        userUuid: user.uuid,
        isDeleted: false
      }
    });

    if (!account) {
      console.log('❌ Social Account Disconnect API - Account not found or unauthorized');
      return NextResponse.json(
        { error: 'Account not found or unauthorized' },
        { status: 404 }
      );
    }

    console.log(`🔌 Social Account Disconnect API - Disconnecting ${account.platform} account: ${account.username}`);

    // Disconnect based on platform
    let disconnectResult;
    switch (account.platform) {
      case 'instagram':
        const instagramService = new InstagramService();
        disconnectResult = await instagramService.disconnectAccount(accountId);
        break;
      default:
        // For other platforms, just mark as inactive
        await prisma.socialAccount.update({
          where: { id: accountId },
          data: {
            isActive: false,
            updatedAt: new Date()
          }
        });
        disconnectResult = {
          success: true,
          message: 'Account disconnected successfully'
        };
        break;
    }

    if (disconnectResult.success) {
      console.log('✅ Social Account Disconnect API - Disconnect completed successfully');
      return NextResponse.json({
        success: true,
        message: disconnectResult.message
      });
    } else {
      console.log('❌ Social Account Disconnect API - Disconnect failed:', disconnectResult.error);
      return NextResponse.json({
        success: false,
        error: disconnectResult.message
      }, { status: 400 });
    }

  } catch (error) {
    console.error('💥 Social Account Disconnect API - Error:', error);
    return NextResponse.json(
      { error: 'Failed to disconnect account' },
      { status: 500 }
    );
  }
}
