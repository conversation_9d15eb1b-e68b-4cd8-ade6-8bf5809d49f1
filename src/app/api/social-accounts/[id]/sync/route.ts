import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';
import { prisma } from '@/lib/prisma';
import { InstagramService } from '@/lib/services/InstagramService';

/**
 * Sync specific social media account
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  console.log(`🔄 Social Account Sync API - Starting sync for account ID: ${params.id}`);
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Social Account Sync API - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ Social Account Sync API - User authenticated:', session.user.email);

    const accountId = parseInt(params.id);
    if (isNaN(accountId)) {
      console.log('❌ Social Account Sync API - Invalid account ID');
      return NextResponse.json(
        { error: 'Invalid account ID' },
        { status: 400 }
      );
    }

    // Find user by UUID
    const user = await prisma.user.findUnique({
      where: { uuid: session.user.uuid }
    });

    if (!user) {
      console.log('❌ Social Account Sync API - User not found in database');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Verify account belongs to user
    const account = await prisma.socialAccount.findFirst({
      where: {
        id: accountId,
        userUuid: user.uuid,
        isDeleted: false
      }
    });

    if (!account) {
      console.log('❌ Social Account Sync API - Account not found or unauthorized');
      return NextResponse.json(
        { error: 'Account not found or unauthorized' },
        { status: 404 }
      );
    }

    console.log(`📊 Social Account Sync API - Syncing ${account.platform} account: ${account.username}`);

    // Sync based on platform
    let syncResult;
    switch (account.platform) {
      case 'instagram':
        const instagramService = new InstagramService();
        syncResult = await instagramService.syncAccountData(accountId);
        break;
      default:
        console.log(`❌ Social Account Sync API - Unsupported platform: ${account.platform}`);
        return NextResponse.json(
          { error: `Sync not supported for platform: ${account.platform}` },
          { status: 400 }
        );
    }

    if (syncResult.success) {
      console.log('✅ Social Account Sync API - Sync completed successfully');
      return NextResponse.json({
        success: true,
        message: syncResult.message,
        data: syncResult.data
      });
    } else {
      console.log('❌ Social Account Sync API - Sync failed:', syncResult.error);
      return NextResponse.json({
        success: false,
        error: syncResult.message
      }, { status: 400 });
    }

  } catch (error) {
    console.error('💥 Social Account Sync API - Error:', error);
    return NextResponse.json(
      { error: 'Failed to sync account' },
      { status: 500 }
    );
  }
}
