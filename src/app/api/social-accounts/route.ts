import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { config } from '@/auth.config';
import { prisma } from '@/lib/prisma';

/**
 * Get user's social media accounts
 */
export async function GET(request: NextRequest) {
  console.log('📋 Social Accounts API - Fetching user accounts');
  
  try {
    // Check if user is authenticated
    const session = await getServerSession(config);
    if (!session?.user) {
      console.log('❌ Social Accounts API - User not authenticated');
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    console.log('✅ Social Accounts API - User authenticated:', session.user.email);

    // Find user by UUID
    const user = await prisma.user.findUnique({
      where: { uuid: session.user.uuid }
    });

    if (!user) {
      console.log('❌ Social Accounts API - User not found in database');
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Fetch user's social accounts
    const socialAccounts = await prisma.socialAccount.findMany({
      where: {
        userUuid: user.uuid,
        isDeleted: false
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`📊 Social Accounts API - Found ${socialAccounts.length} accounts for user`);

    // Transform data for frontend
    const transformedAccounts = socialAccounts.map(account => ({
      id: account.id,
      platform: account.platform,
      username: account.username,
      displayName: account.displayName,
      followers: account.followers,
      following: account.following,
      posts: account.posts,
      status: account.isActive ? 'active' : 'inactive',
      lastSync: account.lastSync,
      health: account.health,
      autoPost: account.autoPost,
      avatarUrl: account.avatarUrl,
      createdAt: account.createdAt
    }));

    return NextResponse.json({
      success: true,
      data: transformedAccounts
    });

  } catch (error) {
    console.error('💥 Social Accounts API - Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch social accounts' },
      { status: 500 }
    );
  }
}
