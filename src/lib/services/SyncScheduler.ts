import { prisma } from '@/lib/prisma';
import { InstagramService } from './InstagramService';

export interface SyncJob {
  id: string;
  accountId: number;
  platform: string;
  scheduledAt: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
  attempts: number;
  maxAttempts: number;
  lastError?: string;
}

/**
 * Background sync scheduler for social media accounts
 * Handles automatic synchronization of account data at regular intervals
 */
export class SyncScheduler {
  private static instance: SyncScheduler;
  private syncJobs: Map<string, SyncJob> = new Map();
  private isRunning = false;
  private intervalId?: NodeJS.Timeout;

  private constructor() {}

  static getInstance(): SyncScheduler {
    if (!SyncScheduler.instance) {
      SyncScheduler.instance = new SyncScheduler();
    }
    return SyncScheduler.instance;
  }

  /**
   * Start the sync scheduler
   */
  start(intervalMinutes: number = 60): void {
    if (this.isRunning) {
      console.log('⚠️ SyncScheduler - Already running');
      return;
    }

    console.log(`🚀 SyncScheduler - Starting with ${intervalMinutes} minute intervals`);
    this.isRunning = true;

    // Run initial sync
    this.runSyncCycle();

    // Schedule periodic syncs
    this.intervalId = setInterval(() => {
      this.runSyncCycle();
    }, intervalMinutes * 60 * 1000);
  }

  /**
   * Stop the sync scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ SyncScheduler - Not running');
      return;
    }

    console.log('🛑 SyncScheduler - Stopping');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  /**
   * Run a sync cycle for all eligible accounts
   */
  private async runSyncCycle(): Promise<void> {
    console.log('🔄 SyncScheduler - Starting sync cycle');

    try {
      // Get all active social accounts that need syncing
      const accounts = await this.getAccountsForSync();
      console.log(`📊 SyncScheduler - Found ${accounts.length} accounts for sync`);

      if (accounts.length === 0) {
        console.log('✅ SyncScheduler - No accounts need syncing');
        return;
      }

      // Process each account
      const syncPromises = accounts.map(account => this.syncAccount(account));
      const results = await Promise.allSettled(syncPromises);

      // Log results
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.length - successful;
      console.log(`📊 SyncScheduler - Sync cycle completed: ${successful} successful, ${failed} failed`);

    } catch (error) {
      console.error('💥 SyncScheduler - Error in sync cycle:', error);
    }
  }

  /**
   * Get accounts that need syncing
   */
  private async getAccountsForSync(): Promise<any[]> {
    const now = new Date();
    const syncThreshold = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago

    return await prisma.socialAccount.findMany({
      where: {
        isActive: true,
        isDeleted: false,
        OR: [
          { lastSync: null },
          { lastSync: { lt: syncThreshold } }
        ]
      },
      include: {
        user: true
      }
    });
  }

  /**
   * Sync a specific account
   */
  private async syncAccount(account: any): Promise<void> {
    const jobId = `${account.platform}-${account.id}-${Date.now()}`;
    console.log(`🔄 SyncScheduler - Syncing account ${jobId}`);

    const job: SyncJob = {
      id: jobId,
      accountId: account.id,
      platform: account.platform,
      scheduledAt: new Date(),
      status: 'running',
      attempts: 1,
      maxAttempts: 3
    };

    this.syncJobs.set(jobId, job);

    try {
      let syncResult;

      switch (account.platform) {
        case 'instagram':
          const instagramService = new InstagramService();
          syncResult = await instagramService.syncAccountData(account.id);
          break;
        default:
          throw new Error(`Unsupported platform: ${account.platform}`);
      }

      if (syncResult.success) {
        job.status = 'completed';
        console.log(`✅ SyncScheduler - Account ${jobId} synced successfully`);
      } else {
        throw new Error(syncResult.error || 'Sync failed');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ SyncScheduler - Account ${jobId} sync failed:`, errorMessage);

      job.status = 'failed';
      job.lastError = errorMessage;

      // Retry logic
      if (job.attempts < job.maxAttempts) {
        console.log(`🔄 SyncScheduler - Retrying account ${jobId} (attempt ${job.attempts + 1}/${job.maxAttempts})`);
        job.attempts++;
        job.status = 'pending';
        
        // Retry after a delay
        setTimeout(() => {
          this.syncAccount(account);
        }, 5 * 60 * 1000); // 5 minutes delay
      } else {
        console.log(`💥 SyncScheduler - Account ${jobId} failed after ${job.maxAttempts} attempts`);
        
        // Update account health on repeated failures
        try {
          await prisma.socialAccount.update({
            where: { id: account.id },
            data: {
              health: Math.max(0, (account.health || 100) - 25),
              lastSync: new Date()
            }
          });
        } catch (dbError) {
          console.error('💥 SyncScheduler - Failed to update account health:', dbError);
        }
      }
    } finally {
      // Clean up completed/failed jobs after some time
      setTimeout(() => {
        this.syncJobs.delete(jobId);
      }, 30 * 60 * 1000); // 30 minutes
    }
  }

  /**
   * Get current sync job status
   */
  getJobStatus(jobId: string): SyncJob | undefined {
    return this.syncJobs.get(jobId);
  }

  /**
   * Get all current sync jobs
   */
  getAllJobs(): SyncJob[] {
    return Array.from(this.syncJobs.values());
  }

  /**
   * Force sync a specific account
   */
  async forceSyncAccount(accountId: number): Promise<void> {
    console.log(`🔄 SyncScheduler - Force syncing account ID: ${accountId}`);

    const account = await prisma.socialAccount.findUnique({
      where: { id: accountId },
      include: { user: true }
    });

    if (!account) {
      throw new Error('Account not found');
    }

    if (!account.isActive || account.isDeleted) {
      throw new Error('Account is not active');
    }

    await this.syncAccount(account);
  }

  /**
   * Get sync statistics
   */
  getSyncStats(): {
    totalJobs: number;
    pendingJobs: number;
    runningJobs: number;
    completedJobs: number;
    failedJobs: number;
  } {
    const jobs = this.getAllJobs();
    
    return {
      totalJobs: jobs.length,
      pendingJobs: jobs.filter(job => job.status === 'pending').length,
      runningJobs: jobs.filter(job => job.status === 'running').length,
      completedJobs: jobs.filter(job => job.status === 'completed').length,
      failedJobs: jobs.filter(job => job.status === 'failed').length
    };
  }
}

// Export singleton instance
export const syncScheduler = SyncScheduler.getInstance();
