import { prisma } from '@/lib/prisma';

export interface InstagramProfile {
  id: string;
  username: string;
  account_type: string;
  media_count: number;
}

export interface InstagramMedia {
  id: string;
  media_type: string;
  media_url: string;
  permalink: string;
  caption?: string;
  timestamp: string;
  like_count?: number;
  comments_count?: number;
}

export interface InstagramAccountMetrics {
  followers_count?: number;
  follows_count?: number;
  media_count: number;
}

export interface SyncResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

/**
 * Instagram Service for handling Instagram API operations
 * Provides methods for OAuth flow, account management, and data synchronization
 */
export class InstagramService {
  private baseURL = 'https://graph.instagram.com';

  /**
   * Get Instagram account profile information
   */
  async getAccountProfile(accessToken: string): Promise<InstagramProfile | null> {
    console.log('📊 InstagramService - Fetching account profile');
    
    try {
      const response = await fetch(
        `${this.baseURL}/me?fields=id,username,account_type,media_count&access_token=${accessToken}`
      );

      if (!response.ok) {
        const errorData = await response.text();
        console.log('❌ InstagramService - Profile fetch failed:', errorData);
        throw new Error(`Failed to fetch profile: ${response.status}`);
      }

      const profileData = await response.json();
      console.log('✅ InstagramService - Profile data retrieved:', {
        id: profileData.id,
        username: profileData.username,
        accountType: profileData.account_type,
        mediaCount: profileData.media_count
      });

      return profileData;
    } catch (error) {
      console.error('💥 InstagramService - Error fetching profile:', error);
      return null;
    }
  }

  /**
   * Get Instagram account metrics (followers, following, etc.)
   */
  async getAccountMetrics(accessToken: string): Promise<InstagramAccountMetrics | null> {
    console.log('📈 InstagramService - Fetching account metrics');
    
    try {
      // Note: Instagram Basic Display API has limited access to follower counts
      // For business accounts, you would need Instagram Graph API
      const response = await fetch(
        `${this.baseURL}/me?fields=media_count&access_token=${accessToken}`
      );

      if (!response.ok) {
        const errorData = await response.text();
        console.log('❌ InstagramService - Metrics fetch failed:', errorData);
        throw new Error(`Failed to fetch metrics: ${response.status}`);
      }

      const metricsData = await response.json();
      console.log('✅ InstagramService - Metrics data retrieved:', metricsData);

      return metricsData;
    } catch (error) {
      console.error('💥 InstagramService - Error fetching metrics:', error);
      return null;
    }
  }

  /**
   * Get user's Instagram media posts
   */
  async getUserMedia(accessToken: string, limit: number = 25): Promise<InstagramMedia[]> {
    console.log(`📸 InstagramService - Fetching user media (limit: ${limit})`);
    
    try {
      const response = await fetch(
        `${this.baseURL}/me/media?fields=id,media_type,media_url,permalink,caption,timestamp&limit=${limit}&access_token=${accessToken}`
      );

      if (!response.ok) {
        const errorData = await response.text();
        console.log('❌ InstagramService - Media fetch failed:', errorData);
        throw new Error(`Failed to fetch media: ${response.status}`);
      }

      const mediaData = await response.json();
      console.log(`✅ InstagramService - Retrieved ${mediaData.data?.length || 0} media items`);

      return mediaData.data || [];
    } catch (error) {
      console.error('💥 InstagramService - Error fetching media:', error);
      return [];
    }
  }

  /**
   * Refresh Instagram access token
   */
  async refreshAccessToken(accessToken: string): Promise<{ access_token: string; expires_in?: number } | null> {
    console.log('🔄 InstagramService - Refreshing access token');
    
    try {
      const response = await fetch(
        `${this.baseURL}/refresh_access_token?grant_type=ig_refresh_token&access_token=${accessToken}`
      );

      if (!response.ok) {
        const errorData = await response.text();
        console.log('❌ InstagramService - Token refresh failed:', errorData);
        throw new Error(`Failed to refresh token: ${response.status}`);
      }

      const tokenData = await response.json();
      console.log('✅ InstagramService - Access token refreshed successfully');

      return tokenData;
    } catch (error) {
      console.error('💥 InstagramService - Error refreshing token:', error);
      return null;
    }
  }

  /**
   * Synchronize Instagram account data
   */
  async syncAccountData(accountId: number): Promise<SyncResult> {
    console.log(`🔄 InstagramService - Starting sync for account ID: ${accountId}`);
    
    try {
      // Get account from database
      const account = await prisma.socialAccount.findUnique({
        where: { id: accountId },
        include: { user: true }
      });

      if (!account) {
        console.log('❌ InstagramService - Account not found');
        return {
          success: false,
          message: 'Account not found',
          error: 'ACCOUNT_NOT_FOUND'
        };
      }

      if (account.platform !== 'instagram') {
        console.log('❌ InstagramService - Invalid platform');
        return {
          success: false,
          message: 'Invalid platform',
          error: 'INVALID_PLATFORM'
        };
      }

      console.log(`📊 InstagramService - Syncing account: ${account.username}`);

      // Check if token needs refresh
      let accessToken = account.accessToken;
      if (account.expiresAt && account.expiresAt < new Date()) {
        console.log('🔄 InstagramService - Token expired, attempting refresh');
        const refreshResult = await this.refreshAccessToken(accessToken);
        if (refreshResult) {
          accessToken = refreshResult.access_token;
          const newExpiresAt = refreshResult.expires_in 
            ? new Date(Date.now() + refreshResult.expires_in * 1000)
            : null;
          
          // Update token in database
          await prisma.socialAccount.update({
            where: { id: accountId },
            data: {
              accessToken: accessToken,
              expiresAt: newExpiresAt
            }
          });
          console.log('✅ InstagramService - Token refreshed and updated');
        } else {
          console.log('❌ InstagramService - Token refresh failed');
          return {
            success: false,
            message: 'Failed to refresh access token',
            error: 'TOKEN_REFRESH_FAILED'
          };
        }
      }

      // Fetch updated profile data
      const profile = await this.getAccountProfile(accessToken);
      if (!profile) {
        console.log('❌ InstagramService - Failed to fetch profile');
        return {
          success: false,
          message: 'Failed to fetch profile data',
          error: 'PROFILE_FETCH_FAILED'
        };
      }

      // Fetch metrics
      const metrics = await this.getAccountMetrics(accessToken);

      // Update account data in database
      const updateData: any = {
        username: profile.username,
        posts: profile.media_count,
        lastSync: new Date(),
        updatedAt: new Date(),
        health: 100 // Reset health on successful sync
      };

      if (metrics) {
        if (metrics.followers_count !== undefined) {
          updateData.followers = metrics.followers_count;
        }
        if (metrics.follows_count !== undefined) {
          updateData.following = metrics.follows_count;
        }
      }

      await prisma.socialAccount.update({
        where: { id: accountId },
        data: updateData
      });

      console.log('✅ InstagramService - Account sync completed successfully');

      return {
        success: true,
        message: 'Account synchronized successfully',
        data: {
          profile,
          metrics,
          lastSync: new Date()
        }
      };

    } catch (error) {
      console.error('💥 InstagramService - Sync error:', error);
      
      // Update account health on error
      try {
        await prisma.socialAccount.update({
          where: { id: accountId },
          data: {
            health: Math.max(0, (await prisma.socialAccount.findUnique({ where: { id: accountId } }))?.health || 100 - 20),
            lastSync: new Date()
          }
        });
      } catch (dbError) {
        console.error('💥 InstagramService - Failed to update account health:', dbError);
      }

      return {
        success: false,
        message: 'Failed to synchronize account',
        error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * Disconnect Instagram account
   */
  async disconnectAccount(accountId: number): Promise<SyncResult> {
    console.log(`🔌 InstagramService - Disconnecting account ID: ${accountId}`);
    
    try {
      const account = await prisma.socialAccount.findUnique({
        where: { id: accountId }
      });

      if (!account) {
        return {
          success: false,
          message: 'Account not found',
          error: 'ACCOUNT_NOT_FOUND'
        };
      }

      // Mark account as inactive
      await prisma.socialAccount.update({
        where: { id: accountId },
        data: {
          isActive: false,
          updatedAt: new Date()
        }
      });

      console.log('✅ InstagramService - Account disconnected successfully');

      return {
        success: true,
        message: 'Account disconnected successfully'
      };

    } catch (error) {
      console.error('💥 InstagramService - Disconnect error:', error);
      return {
        success: false,
        message: 'Failed to disconnect account',
        error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
      };
    }
  }
}
