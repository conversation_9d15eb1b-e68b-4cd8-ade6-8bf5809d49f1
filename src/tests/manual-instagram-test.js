/**
 * Manual Instagram Integration Test Script
 * 
 * This script provides manual testing capabilities for the Instagram OAuth integration.
 * Run this script to test various aspects of the Instagram integration without
 * going through the full OAuth flow.
 * 
 * Usage: node src/tests/manual-instagram-test.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Test configuration
const TEST_CONFIG = {
  // Replace with actual test values when testing
  TEST_ACCESS_TOKEN: process.env.INSTAGRAM_TEST_ACCESS_TOKEN || 'test_token',
  TEST_USER_UUID: process.env.TEST_USER_UUID || 'test-user-uuid',
  INSTAGRAM_CLIENT_ID: process.env.INSTAGRAM_CLIENT_ID || 'test_client_id',
  INSTAGRAM_CLIENT_SECRET: process.env.INSTAGRAM_CLIENT_SECRET || 'test_client_secret',
  INSTAGRAM_REDIRECT_URI: process.env.INSTAGRAM_REDIRECT_URI || 'http://localhost:3000/api/auth/instagram/callback'
};

console.log('🧪 Instagram Integration Manual Test Suite');
console.log('==========================================');

/**
 * Test database connection and schema
 */
async function testDatabaseConnection() {
  console.log('\n📊 Testing Database Connection...');
  
  try {
    // Test connection
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Check if required tables exist
    const userCount = await prisma.user.count();
    console.log(`✅ Users table accessible (${userCount} users)`);

    const socialAccountCount = await prisma.socialAccount.count();
    console.log(`✅ SocialAccount table accessible (${socialAccountCount} accounts)`);

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

/**
 * Test Instagram API endpoints (mock)
 */
async function testInstagramAPI() {
  console.log('\n🔗 Testing Instagram API Integration...');
  
  try {
    // Test profile endpoint format
    const profileUrl = `https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${TEST_CONFIG.TEST_ACCESS_TOKEN}`;
    console.log('✅ Profile endpoint URL format correct:', profileUrl);

    // Test media endpoint format
    const mediaUrl = `https://graph.instagram.com/me/media?fields=id,media_type,media_url,permalink,caption,timestamp&limit=25&access_token=${TEST_CONFIG.TEST_ACCESS_TOKEN}`;
    console.log('✅ Media endpoint URL format correct');

    // Test token refresh endpoint format
    const refreshUrl = `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${TEST_CONFIG.TEST_ACCESS_TOKEN}`;
    console.log('✅ Token refresh endpoint URL format correct');

    return true;
  } catch (error) {
    console.error('❌ Instagram API test failed:', error.message);
    return false;
  }
}

/**
 * Test OAuth flow components
 */
async function testOAuthFlow() {
  console.log('\n🔐 Testing OAuth Flow Components...');
  
  try {
    // Test state parameter generation
    const testUserId = 'test-user-123';
    const timestamp = Date.now();
    const state = Buffer.from(JSON.stringify({
      userId: testUserId,
      timestamp: timestamp
    })).toString('base64');
    
    console.log('✅ State parameter generated:', state);

    // Test state parameter decoding
    const decoded = JSON.parse(Buffer.from(state, 'base64').toString());
    if (decoded.userId === testUserId && decoded.timestamp === timestamp) {
      console.log('✅ State parameter decoding successful');
    } else {
      throw new Error('State parameter decoding failed');
    }

    // Test authorization URL generation
    const authUrl = new URL('https://api.instagram.com/oauth/authorize');
    authUrl.searchParams.set('client_id', TEST_CONFIG.INSTAGRAM_CLIENT_ID);
    authUrl.searchParams.set('redirect_uri', TEST_CONFIG.INSTAGRAM_REDIRECT_URI);
    authUrl.searchParams.set('scope', 'user_profile,user_media');
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('state', state);

    console.log('✅ Authorization URL generated:', authUrl.toString());

    return true;
  } catch (error) {
    console.error('❌ OAuth flow test failed:', error.message);
    return false;
  }
}

/**
 * Test social account data operations
 */
async function testSocialAccountOperations() {
  console.log('\n👤 Testing Social Account Operations...');
  
  try {
    // Create test user if not exists
    let testUser = await prisma.user.findUnique({
      where: { uuid: TEST_CONFIG.TEST_USER_UUID }
    });

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          uuid: TEST_CONFIG.TEST_USER_UUID,
          email: '<EMAIL>',
          nickname: 'Test User',
          signinProvider: 'test'
        }
      });
      console.log('✅ Test user created');
    } else {
      console.log('✅ Test user found');
    }

    // Test social account creation
    const testAccount = await prisma.socialAccount.create({
      data: {
        userUuid: testUser.uuid,
        platform: 'instagram',
        accountId: 'test_instagram_id',
        username: 'test_instagram_user',
        displayName: 'Test Instagram User',
        accessToken: 'test_access_token',
        isActive: true,
        health: 100
      }
    });
    console.log('✅ Test social account created:', testAccount.id);

    // Test social account retrieval
    const retrievedAccount = await prisma.socialAccount.findUnique({
      where: { id: testAccount.id },
      include: { user: true }
    });
    
    if (retrievedAccount) {
      console.log('✅ Social account retrieval successful');
    } else {
      throw new Error('Failed to retrieve social account');
    }

    // Test social account update
    await prisma.socialAccount.update({
      where: { id: testAccount.id },
      data: {
        lastSync: new Date(),
        posts: 150,
        followers: 1000
      }
    });
    console.log('✅ Social account update successful');

    // Clean up test data
    await prisma.socialAccount.delete({
      where: { id: testAccount.id }
    });
    console.log('✅ Test social account cleaned up');

    return true;
  } catch (error) {
    console.error('❌ Social account operations test failed:', error.message);
    return false;
  }
}

/**
 * Test sync functionality
 */
async function testSyncFunctionality() {
  console.log('\n🔄 Testing Sync Functionality...');
  
  try {
    // Test time calculations
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const diffMins = Math.floor((now.getTime() - oneHourAgo.getTime()) / (1000 * 60));
    const diffHours = Math.floor((now.getTime() - oneDayAgo.getTime()) / (1000 * 60 * 60));

    if (diffMins === 60 && diffHours === 24) {
      console.log('✅ Time calculations correct');
    } else {
      throw new Error('Time calculations incorrect');
    }

    // Test health calculations
    const baseHealth = 100;
    const errorPenalty = 20;
    const newHealth = Math.max(0, Math.min(100, baseHealth - errorPenalty));
    
    if (newHealth === 80) {
      console.log('✅ Health calculations correct');
    } else {
      throw new Error('Health calculations incorrect');
    }

    // Test sync threshold logic
    const syncThreshold = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour ago
    const needsSync = oneHourAgo < syncThreshold;
    
    if (needsSync) {
      console.log('✅ Sync threshold logic correct');
    } else {
      console.log('⚠️ Sync threshold logic needs review');
    }

    return true;
  } catch (error) {
    console.error('❌ Sync functionality test failed:', error.message);
    return false;
  }
}

/**
 * Test error handling scenarios
 */
async function testErrorHandling() {
  console.log('\n🚨 Testing Error Handling...');
  
  try {
    // Test invalid account ID handling
    try {
      await prisma.socialAccount.findUnique({
        where: { id: 999999 }
      });
      console.log('✅ Invalid account ID handled gracefully');
    } catch (error) {
      console.log('✅ Database error handling working');
    }

    // Test invalid UUID handling
    try {
      await prisma.user.findUnique({
        where: { uuid: 'invalid-uuid-format' }
      });
      console.log('✅ Invalid UUID handled gracefully');
    } catch (error) {
      console.log('✅ UUID validation working');
    }

    // Test data validation
    const validationTests = [
      { field: 'platform', value: 'instagram', valid: true },
      { field: 'platform', value: '', valid: false },
      { field: 'health', value: 100, valid: true },
      { field: 'health', value: -10, valid: false },
      { field: 'health', value: 150, valid: false }
    ];

    validationTests.forEach(test => {
      if (test.field === 'health') {
        const isValid = test.value >= 0 && test.value <= 100;
        if (isValid === test.valid) {
          console.log(`✅ ${test.field} validation correct for value: ${test.value}`);
        } else {
          console.log(`❌ ${test.field} validation incorrect for value: ${test.value}`);
        }
      }
    });

    return true;
  } catch (error) {
    console.error('❌ Error handling test failed:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Instagram Integration Tests...\n');
  
  const tests = [
    { name: 'Database Connection', fn: testDatabaseConnection },
    { name: 'Instagram API', fn: testInstagramAPI },
    { name: 'OAuth Flow', fn: testOAuthFlow },
    { name: 'Social Account Operations', fn: testSocialAccountOperations },
    { name: 'Sync Functionality', fn: testSyncFunctionality },
    { name: 'Error Handling', fn: testErrorHandling }
  ];

  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, success: result });
    } catch (error) {
      console.error(`💥 Test "${test.name}" crashed:`, error.message);
      results.push({ name: test.name, success: false });
    }
  }

  // Print summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
  });
  
  console.log(`\n🎯 Overall: ${successful}/${total} tests passed`);
  
  if (successful === total) {
    console.log('🎉 All tests passed! Instagram integration is ready.');
  } else {
    console.log('⚠️ Some tests failed. Please review the implementation.');
  }

  await prisma.$disconnect();
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testDatabaseConnection,
  testInstagramAPI,
  testOAuthFlow,
  testSocialAccountOperations,
  testSyncFunctionality,
  testErrorHandling,
  runAllTests
};
