import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { InstagramService } from '@/lib/services/InstagramService';
import { prisma } from '@/lib/prisma';

// Mock fetch globally
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

describe('Instagram Integration Tests', () => {
  let instagramService: InstagramService;
  const mockAccessToken = 'mock_access_token';
  const mockAccountId = 1;

  beforeEach(() => {
    instagramService = new InstagramService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('InstagramService', () => {
    describe('getAccountProfile', () => {
      it('should fetch Instagram profile successfully', async () => {
        const mockProfile = {
          id: '12345',
          username: 'test_user',
          account_type: 'PERSONAL',
          media_count: 100
        };

        (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
          ok: true,
          json: async () => mockProfile
        } as Response);

        const result = await instagramService.getAccountProfile(mockAccessToken);

        expect(fetch).toHaveBeenCalledWith(
          `https://graph.instagram.com/me?fields=id,username,account_type,media_count&access_token=${mockAccessToken}`
        );
        expect(result).toEqual(mockProfile);
      });

      it('should handle API errors gracefully', async () => {
        (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
          ok: false,
          status: 400,
          text: async () => 'Bad Request'
        } as Response);

        const result = await instagramService.getAccountProfile(mockAccessToken);

        expect(result).toBeNull();
      });

      it('should handle network errors', async () => {
        (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValueOnce(
          new Error('Network error')
        );

        const result = await instagramService.getAccountProfile(mockAccessToken);

        expect(result).toBeNull();
      });
    });

    describe('getAccountMetrics', () => {
      it('should fetch Instagram metrics successfully', async () => {
        const mockMetrics = {
          media_count: 150
        };

        (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
          ok: true,
          json: async () => mockMetrics
        } as Response);

        const result = await instagramService.getAccountMetrics(mockAccessToken);

        expect(fetch).toHaveBeenCalledWith(
          `https://graph.instagram.com/me?fields=media_count&access_token=${mockAccessToken}`
        );
        expect(result).toEqual(mockMetrics);
      });
    });

    describe('getUserMedia', () => {
      it('should fetch user media successfully', async () => {
        const mockMedia = {
          data: [
            {
              id: 'media1',
              media_type: 'IMAGE',
              media_url: 'https://example.com/image1.jpg',
              permalink: 'https://instagram.com/p/abc123',
              caption: 'Test caption',
              timestamp: '2024-01-01T00:00:00Z'
            }
          ]
        };

        (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
          ok: true,
          json: async () => mockMedia
        } as Response);

        const result = await instagramService.getUserMedia(mockAccessToken, 10);

        expect(fetch).toHaveBeenCalledWith(
          `https://graph.instagram.com/me/media?fields=id,media_type,media_url,permalink,caption,timestamp&limit=10&access_token=${mockAccessToken}`
        );
        expect(result).toEqual(mockMedia.data);
      });
    });

    describe('refreshAccessToken', () => {
      it('should refresh access token successfully', async () => {
        const mockTokenResponse = {
          access_token: 'new_access_token',
          expires_in: 5184000
        };

        (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
          ok: true,
          json: async () => mockTokenResponse
        } as Response);

        const result = await instagramService.refreshAccessToken(mockAccessToken);

        expect(fetch).toHaveBeenCalledWith(
          `https://graph.instagram.com/refresh_access_token?grant_type=ig_refresh_token&access_token=${mockAccessToken}`
        );
        expect(result).toEqual(mockTokenResponse);
      });
    });

    describe('syncAccountData', () => {
      beforeEach(() => {
        // Mock Prisma methods
        jest.spyOn(prisma.socialAccount, 'findUnique').mockImplementation();
        jest.spyOn(prisma.socialAccount, 'update').mockImplementation();
      });

      it('should sync account data successfully', async () => {
        const mockAccount = {
          id: mockAccountId,
          platform: 'instagram',
          username: 'test_user',
          accessToken: mockAccessToken,
          expiresAt: new Date(Date.now() + ********), // 1 day from now
          user: { uuid: 'user-uuid' }
        };

        const mockProfile = {
          id: '12345',
          username: 'test_user',
          account_type: 'PERSONAL',
          media_count: 100
        };

        const mockMetrics = {
          media_count: 100
        };

        (prisma.socialAccount.findUnique as jest.Mock).mockResolvedValueOnce(mockAccount);
        (prisma.socialAccount.update as jest.Mock).mockResolvedValueOnce({});

        // Mock API calls
        (fetch as jest.MockedFunction<typeof fetch>)
          .mockResolvedValueOnce({
            ok: true,
            json: async () => mockProfile
          } as Response)
          .mockResolvedValueOnce({
            ok: true,
            json: async () => mockMetrics
          } as Response);

        const result = await instagramService.syncAccountData(mockAccountId);

        expect(result.success).toBe(true);
        expect(result.message).toBe('Account synchronized successfully');
        expect(prisma.socialAccount.update).toHaveBeenCalledWith({
          where: { id: mockAccountId },
          data: expect.objectContaining({
            username: mockProfile.username,
            posts: mockProfile.media_count,
            health: 100
          })
        });
      });

      it('should handle account not found', async () => {
        (prisma.socialAccount.findUnique as jest.Mock).mockResolvedValueOnce(null);

        const result = await instagramService.syncAccountData(mockAccountId);

        expect(result.success).toBe(false);
        expect(result.error).toBe('ACCOUNT_NOT_FOUND');
      });

      it('should handle invalid platform', async () => {
        const mockAccount = {
          id: mockAccountId,
          platform: 'twitter',
          username: 'test_user',
          accessToken: mockAccessToken,
          user: { uuid: 'user-uuid' }
        };

        (prisma.socialAccount.findUnique as jest.Mock).mockResolvedValueOnce(mockAccount);

        const result = await instagramService.syncAccountData(mockAccountId);

        expect(result.success).toBe(false);
        expect(result.error).toBe('INVALID_PLATFORM');
      });

      it('should refresh expired token', async () => {
        const mockAccount = {
          id: mockAccountId,
          platform: 'instagram',
          username: 'test_user',
          accessToken: mockAccessToken,
          expiresAt: new Date(Date.now() - ********), // 1 day ago (expired)
          user: { uuid: 'user-uuid' }
        };

        const mockTokenResponse = {
          access_token: 'new_access_token',
          expires_in: 5184000
        };

        const mockProfile = {
          id: '12345',
          username: 'test_user',
          account_type: 'PERSONAL',
          media_count: 100
        };

        (prisma.socialAccount.findUnique as jest.Mock).mockResolvedValueOnce(mockAccount);
        (prisma.socialAccount.update as jest.Mock).mockResolvedValue({});

        // Mock token refresh and profile fetch
        (fetch as jest.MockedFunction<typeof fetch>)
          .mockResolvedValueOnce({
            ok: true,
            json: async () => mockTokenResponse
          } as Response)
          .mockResolvedValueOnce({
            ok: true,
            json: async () => mockProfile
          } as Response)
          .mockResolvedValueOnce({
            ok: true,
            json: async () => ({ media_count: 100 })
          } as Response);

        const result = await instagramService.syncAccountData(mockAccountId);

        expect(result.success).toBe(true);
        expect(prisma.socialAccount.update).toHaveBeenCalledWith({
          where: { id: mockAccountId },
          data: expect.objectContaining({
            accessToken: mockTokenResponse.access_token
          })
        });
      });
    });

    describe('disconnectAccount', () => {
      it('should disconnect account successfully', async () => {
        const mockAccount = {
          id: mockAccountId,
          platform: 'instagram',
          username: 'test_user'
        };

        (prisma.socialAccount.findUnique as jest.Mock).mockResolvedValueOnce(mockAccount);
        (prisma.socialAccount.update as jest.Mock).mockResolvedValueOnce({});

        const result = await instagramService.disconnectAccount(mockAccountId);

        expect(result.success).toBe(true);
        expect(result.message).toBe('Account disconnected successfully');
        expect(prisma.socialAccount.update).toHaveBeenCalledWith({
          where: { id: mockAccountId },
          data: {
            isActive: false,
            updatedAt: expect.any(Date)
          }
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      (prisma.socialAccount.findUnique as jest.Mock).mockRejectedValueOnce(
        new Error('Database connection failed')
      );

      const result = await instagramService.syncAccountData(mockAccountId);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database connection failed');
    });

    it('should handle Instagram API rate limiting', async () => {
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: false,
        status: 429,
        text: async () => 'Rate limit exceeded'
      } as Response);

      const result = await instagramService.getAccountProfile(mockAccessToken);

      expect(result).toBeNull();
    });

    it('should handle invalid access tokens', async () => {
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: false,
        status: 401,
        text: async () => 'Invalid access token'
      } as Response);

      const result = await instagramService.getAccountProfile('invalid_token');

      expect(result).toBeNull();
    });
  });

  describe('OAuth Flow Integration', () => {
    it('should validate OAuth state parameter format', () => {
      const testUserId = 'user-123';
      const timestamp = Date.now();
      const state = Buffer.from(JSON.stringify({
        userId: testUserId,
        timestamp: timestamp
      })).toString('base64');

      const decoded = JSON.parse(Buffer.from(state, 'base64').toString());

      expect(decoded.userId).toBe(testUserId);
      expect(decoded.timestamp).toBe(timestamp);
    });

    it('should handle OAuth callback parameters correctly', () => {
      const mockCode = 'auth_code_123';
      const mockState = Buffer.from(JSON.stringify({
        userId: 'user-123',
        timestamp: Date.now()
      })).toString('base64');

      // Simulate URL parameters
      const searchParams = new URLSearchParams({
        code: mockCode,
        state: mockState
      });

      expect(searchParams.get('code')).toBe(mockCode);
      expect(searchParams.get('state')).toBe(mockState);
    });

    it('should validate Instagram authorization URL format', () => {
      const clientId = 'test_client_id';
      const redirectUri = 'http://localhost:3000/api/auth/instagram/callback';
      const state = 'test_state';

      const authUrl = new URL('https://api.instagram.com/oauth/authorize');
      authUrl.searchParams.set('client_id', clientId);
      authUrl.searchParams.set('redirect_uri', redirectUri);
      authUrl.searchParams.set('scope', 'user_profile,user_media');
      authUrl.searchParams.set('response_type', 'code');
      authUrl.searchParams.set('state', state);

      expect(authUrl.toString()).toContain('api.instagram.com/oauth/authorize');
      expect(authUrl.searchParams.get('client_id')).toBe(clientId);
      expect(authUrl.searchParams.get('redirect_uri')).toBe(redirectUri);
      expect(authUrl.searchParams.get('scope')).toBe('user_profile,user_media');
      expect(authUrl.searchParams.get('response_type')).toBe('code');
      expect(authUrl.searchParams.get('state')).toBe(state);
    });
  });

  describe('Data Synchronization', () => {
    it('should format sync timestamps correctly', () => {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Test time difference calculations
      const diffMins = Math.floor((now.getTime() - oneHourAgo.getTime()) / (1000 * 60));
      const diffHours = Math.floor((now.getTime() - oneDayAgo.getTime()) / (1000 * 60 * 60));

      expect(diffMins).toBe(60);
      expect(diffHours).toBe(24);
    });

    it('should validate account health calculations', () => {
      const baseHealth = 100;
      const errorPenalty = 20;
      const maxHealth = 100;
      const minHealth = 0;

      const newHealth = Math.max(minHealth, Math.min(maxHealth, baseHealth - errorPenalty));

      expect(newHealth).toBe(80);
      expect(newHealth).toBeGreaterThanOrEqual(minHealth);
      expect(newHealth).toBeLessThanOrEqual(maxHealth);
    });
  });
});
