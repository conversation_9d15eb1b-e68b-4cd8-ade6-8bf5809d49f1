generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  // 自增主键ID
  id             Int       @id @default(autoincrement())
  // 用户唯一标识UUID
  uuid           String    @unique
  // 用户邮箱
  email          String
  // 密码哈希，可选（用于邮箱密码登录）
  password       String?   @db.VarChar(255)
  // 创建时间，自动生成当前时间
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 更新时间，自动更新
  updatedAt      DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted      Boolean   @default(false) @map("is_deleted")
  // 用户昵称，可选
  nickname       String?   @db.VarChar(255)
  // 用户头像URL，可选
  avatarUrl      String?   @map("avatar_url") @db.VarChar(255)
  // 用户区域/语言设置，可选
  locale         String?   @db.VarChar(50)
  // 登录类型，可选
  signinType     String?   @map("signin_type") @db.VarChar(50)
  // 登录IP地址，可选
  signinIp       String?   @map("signin_ip") @db.VarChar(255)
  // 登录提供商(如Google、Facebook等)，可选
  signinProvider String?   @map("signin_provider") @db.VarChar(50)
  // 第三方登录的OpenID，可选
  signinOpenid   String?   @map("signin_openid") @db.VarChar(255)
  // 用户订单关联
  orders         Order[]
  // 用户社交媒体账户关联
  socialAccounts SocialAccount[]
  // 用户内容关联
  contents       Content[]
  // 用户发布记录关联
  publications   Publication[]

  // 联合唯一索引：确保同一登录提供商下邮箱唯一
  @@unique([email, signinProvider])
  // 数据库表映射名称
  @@map("users")
}

model Order {
  // 自增主键ID
  id               Int       @id @default(autoincrement())
  // 订单编号，唯一
  orderNo          String    @unique @map("order_no") @db.VarChar(255)
  // 订单创建时间
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 订单更新时间
  updatedAt        DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted        Boolean   @default(false) @map("is_deleted")
  // 用户UUID，关联User表
  userUuid         String    @map("user_uuid") @db.VarChar(255)
  // 用户邮箱
  userEmail        String    @map("user_email") @db.VarChar(255)
  // 订单金额
  amount           Int
  // 计费周期间隔（月、年等）
  interval         String?   @db.VarChar(50)
  // 订单过期时间
  expiredAt        DateTime? @map("expired_at") @db.Timestamptz(6)
  // 订单状态
  status           String    @db.VarChar(50)
  // Stripe支付会话ID
  stripeSessionId  String?   @map("stripe_session_id") @db.VarChar(255)
  // 订单包含的积分数量
  credits          Int
  // 货币类型
  currency         String?   @db.VarChar(50)
  // 订阅ID
  subId            String?   @map("sub_id") @db.VarChar(255)
  // 订阅间隔计数
  subIntervalCount Int?      @map("sub_interval_count")
  // 订阅周期锚点
  subCycleAnchor   Int?      @map("sub_cycle_anchor")
  // 订阅周期结束时间
  subPeriodEnd     Int?      @map("sub_period_end")
  // 订阅周期开始时间
  subPeriodStart   Int?      @map("sub_period_start")
  // 订阅次数
  subTimes         Int?      @map("sub_times")
  // 产品ID
  productId        String?   @map("product_id") @db.VarChar(255)
  // 产品名称
  productName      String?   @map("product_name") @db.VarChar(255)
  // 有效月数
  validMonths      Int?      @map("valid_months")
  // 订单详情（可存储JSON）
  orderDetail      String?   @map("order_detail")
  // 支付时间
  paidAt           DateTime? @map("paid_at") @db.Timestamptz(6)
  // 支付邮箱
  paidEmail        String?   @map("paid_email") @db.VarChar(255)
  // 支付详情
  paidDetail       String?   @map("paid_detail")
  // 关联用户
  user             User      @relation(fields: [userUuid], references: [uuid])

  // 数据库表映射名称
  @@map("orders")
}

// 社交媒体账户模型
model SocialAccount {
  // 自增主键ID
  id           Int       @id @default(autoincrement())
  // 用户UUID，关联User表
  userUuid     String    @map("user_uuid") @db.VarChar(255)
  // 平台名称 (instagram, tiktok, linkedin, twitter等)
  platform     String    @db.VarChar(50)
  // 平台账户ID
  accountId    String    @map("account_id") @db.VarChar(255)
  // 平台用户名
  username     String?   @db.VarChar(255)
  // 显示名称
  displayName  String?   @map("display_name") @db.VarChar(255)
  // 访问令牌
  accessToken  String    @map("access_token") @db.Text
  // 刷新令牌
  refreshToken String?   @map("refresh_token") @db.Text
  // 令牌过期时间
  expiresAt    DateTime? @map("expires_at") @db.Timestamptz(6)
  // 账户是否激活
  isActive     Boolean   @default(true) @map("is_active")
  // 账户头像URL
  avatarUrl    String?   @map("avatar_url") @db.VarChar(255)
  // 粉丝数量
  followers    Int?      @default(0)
  // 关注数量
  following    Int?      @default(0)
  // 帖子数量
  posts        Int?      @default(0)
  // 账户健康度 (0-100)
  health       Int?      @default(100)
  // 是否启用自动发布
  autoPost     Boolean   @default(false) @map("auto_post")
  // 最后同步时间
  lastSync     DateTime? @map("last_sync") @db.Timestamptz(6)
  // 创建时间
  createdAt    DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 更新时间
  updatedAt    DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted    Boolean   @default(false) @map("is_deleted")

  // 关联用户
  user         User          @relation(fields: [userUuid], references: [uuid], onDelete: Cascade)
  // 关联发布记录
  publications Publication[]

  // 联合唯一索引：确保同一用户下同一平台的账户唯一
  @@unique([userUuid, platform, accountId])
  // 数据库表映射名称
  @@map("social_accounts")
}

// 内容模型
model Content {
  // 自增主键ID
  id              Int      @id @default(autoincrement())
  // 用户UUID，关联User表
  userUuid        String   @map("user_uuid") @db.VarChar(255)
  // 内容标题
  title           String   @db.VarChar(500)
  // 内容描述
  description     String?  @db.Text
  // 内容类型 (image, video, carousel等)
  contentType     String   @map("content_type") @db.VarChar(50)
  // 媒体文件URLs (JSON格式)
  mediaUrls       String?  @map("media_urls") @db.Text
  // 平台特定配置 (JSON格式)
  platformConfigs String?  @map("platform_configs") @db.Text
  // 内容状态 (draft, scheduled, published, failed)
  status          String   @default("draft") @db.VarChar(50)
  // 创建时间
  createdAt       DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  // 更新时间
  updatedAt       DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted       Boolean  @default(false) @map("is_deleted")

  // 关联用户
  user         User          @relation(fields: [userUuid], references: [uuid], onDelete: Cascade)
  // 关联发布记录
  publications Publication[]

  // 数据库表映射名称
  @@map("contents")
}

// 发布记录模型
model Publication {
  // 自增主键ID
  id              Int       @id @default(autoincrement())
  // 内容ID，关联Content表
  contentId       Int       @map("content_id")
  // 社交账户ID，关联SocialAccount表
  socialAccountId Int       @map("social_account_id")
  // 平台帖子ID
  platformPostId  String?   @map("platform_post_id") @db.VarChar(255)
  // 计划发布时间
  scheduledAt     DateTime? @map("scheduled_at") @db.Timestamptz(6)
  // 实际发布时间
  publishedAt     DateTime? @map("published_at") @db.Timestamptz(6)
  // 发布状态 (pending, scheduled, published, failed)
  status          String    @default("pending") @db.VarChar(50)
  // 错误信息
  errorMessage    String?   @map("error_message") @db.Text
  // 创建时间
  createdAt       DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 更新时间
  updatedAt       DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)

  // 关联内容
  content       Content       @relation(fields: [contentId], references: [id], onDelete: Cascade)
  // 关联社交账户
  socialAccount SocialAccount @relation(fields: [socialAccountId], references: [id], onDelete: Cascade)

  // 数据库表映射名称
  @@map("publications")
}