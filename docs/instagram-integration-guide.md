# Instagram Integration Guide

This guide provides comprehensive documentation for the Instagram account linking and authorization functionality implemented in ContentFlow.

## 🎯 Overview

The Instagram integration allows users to:
- Connect their Instagram accounts via OAuth 2.0
- Synchronize account data (profile, metrics, posts)
- Manage multiple Instagram accounts
- Monitor account health and sync status
- Automatically refresh access tokens

## 🏗️ Architecture

### Components

1. **OAuth Flow**
   - Authorization endpoint: `/api/auth/instagram/authorize`
   - Callback endpoint: `/api/auth/instagram/callback`
   - State parameter validation for security

2. **Service Layer**
   - `InstagramService`: Core Instagram API integration
   - `SyncScheduler`: Background data synchronization
   - Comprehensive logging and error handling

3. **Database Schema**
   - `SocialAccount`: Store account credentials and metadata
   - `Content`: Manage content for publishing
   - `Publication`: Track publishing history

4. **UI Components**
   - Enhanced `AccountManager`: Real-time account management
   - OAuth integration with loading states
   - Error handling and user feedback

## 🔧 Setup Instructions

### 1. Instagram App Configuration

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app and add Instagram Basic Display product
3. Configure OAuth redirect URIs:
   ```
   http://localhost:3000/api/auth/instagram/callback
   https://yourdomain.com/api/auth/instagram/callback
   ```

### 2. Environment Variables

Add to your `.env` file:
```bash
# Instagram OAuth Configuration
INSTAGRAM_CLIENT_ID=your_instagram_client_id
INSTAGRAM_CLIENT_SECRET=your_instagram_client_secret
INSTAGRAM_REDIRECT_URI=http://localhost:3000/api/auth/instagram/callback
NEXT_PUBLIC_INSTAGRAM_ENABLED=true
```

### 3. Database Migration

Run Prisma migration to update the database schema:
```bash
npx prisma db push
npx prisma generate
```

## 📊 Database Schema

### SocialAccount Model
```prisma
model SocialAccount {
  id           Int       @id @default(autoincrement())
  userUuid     String    @map("user_uuid")
  platform     String    // 'instagram'
  accountId    String    @map("account_id")
  username     String?
  displayName  String?   @map("display_name")
  accessToken  String    @map("access_token")
  refreshToken String?   @map("refresh_token")
  expiresAt    DateTime? @map("expires_at")
  isActive     Boolean   @default(true)
  avatarUrl    String?   @map("avatar_url")
  followers    Int?      @default(0)
  following    Int?      @default(0)
  posts        Int?      @default(0)
  health       Int?      @default(100)
  autoPost     Boolean   @default(false)
  lastSync     DateTime? @map("last_sync")
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  isDeleted    Boolean   @default(false)
  
  user         User          @relation(fields: [userUuid], references: [uuid])
  publications Publication[]
  
  @@unique([userUuid, platform, accountId])
  @@map("social_accounts")
}
```

## 🔄 OAuth Flow

### Authorization Process

1. **User clicks "Connect Instagram"**
   ```typescript
   // Redirect to authorization endpoint
   window.location.href = '/api/auth/instagram/authorize';
   ```

2. **Authorization endpoint generates secure state**
   ```typescript
   const state = Buffer.from(JSON.stringify({
     userId: session.user.id,
     timestamp: Date.now()
   })).toString('base64');
   ```

3. **Redirect to Instagram**
   ```
   https://api.instagram.com/oauth/authorize?
     client_id=CLIENT_ID&
     redirect_uri=REDIRECT_URI&
     scope=user_profile,user_media&
     response_type=code&
     state=STATE_PARAMETER
   ```

4. **Callback processing**
   - Validate state parameter
   - Exchange code for access token
   - Get long-lived token
   - Fetch user profile
   - Store account in database

### Security Features

- **State Parameter Validation**: Prevents CSRF attacks
- **User Session Verification**: Ensures authenticated user
- **Token Encryption**: Secure storage of access tokens
- **Error Handling**: Comprehensive error scenarios

## 📡 API Endpoints

### Account Management

#### Get User's Social Accounts
```http
GET /api/social-accounts
Authorization: Bearer <session_token>
```

Response:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "platform": "instagram",
      "username": "@username",
      "displayName": "Display Name",
      "followers": 1000,
      "following": 500,
      "posts": 100,
      "status": "active",
      "lastSync": "2024-01-01T12:00:00Z",
      "health": 100,
      "autoPost": true
    }
  ]
}
```

#### Sync Account
```http
POST /api/social-accounts/{id}/sync
Authorization: Bearer <session_token>
```

#### Disconnect Account
```http
POST /api/social-accounts/{id}/disconnect
Authorization: Bearer <session_token>
```

#### Sync All Accounts
```http
POST /api/social-accounts/sync-all
Authorization: Bearer <session_token>
```

## 🔄 Data Synchronization

### InstagramService Methods

#### Get Account Profile
```typescript
const profile = await instagramService.getAccountProfile(accessToken);
// Returns: { id, username, account_type, media_count }
```

#### Get Account Metrics
```typescript
const metrics = await instagramService.getAccountMetrics(accessToken);
// Returns: { media_count, followers_count?, follows_count? }
```

#### Sync Account Data
```typescript
const result = await instagramService.syncAccountData(accountId);
// Returns: { success, message, data?, error? }
```

### Background Sync Scheduler

The `SyncScheduler` automatically synchronizes account data:

```typescript
import { syncScheduler } from '@/lib/services/SyncScheduler';

// Start scheduler (runs every 60 minutes by default)
syncScheduler.start(60);

// Force sync specific account
await syncScheduler.forceSyncAccount(accountId);

// Get sync statistics
const stats = syncScheduler.getSyncStats();
```

## 🧪 Testing

### Automated Tests

Run the test suite:
```bash
npm test src/tests/instagram-integration.test.ts
```

### Manual Testing

Run the manual test script:
```bash
node src/tests/manual-instagram-test.js
```

Test coverage includes:
- ✅ Database connection and schema
- ✅ Instagram API integration
- ✅ OAuth flow components
- ✅ Social account operations
- ✅ Sync functionality
- ✅ Error handling scenarios

## 🚨 Error Handling

### Common Error Scenarios

1. **OAuth Errors**
   - `instagram_oauth_denied`: User denied authorization
   - `instagram_oauth_invalid`: Invalid authorization parameters
   - `instagram_token_exchange_failed`: Token exchange failed

2. **API Errors**
   - Rate limiting (429)
   - Invalid access token (401)
   - Account not found (404)

3. **Sync Errors**
   - Token expiration
   - Network connectivity issues
   - Database connection failures

### Error Recovery

- **Automatic token refresh** for expired tokens
- **Retry logic** with exponential backoff
- **Health monitoring** with degraded account status
- **Comprehensive logging** for debugging

## 📊 Monitoring and Logging

### Log Levels

All operations include detailed logging:
```
🚀 Starting operation
📊 Data retrieved
✅ Operation successful
❌ Operation failed
💥 Critical error
🔄 Retry attempt
```

### Health Monitoring

Account health is tracked (0-100%):
- **100%**: Fully operational
- **75-99%**: Minor issues
- **50-74%**: Degraded performance
- **0-49%**: Critical issues

## 🔒 Security Considerations

1. **Access Token Storage**: Encrypted in database
2. **State Parameter**: CSRF protection
3. **Session Validation**: User authentication required
4. **Rate Limiting**: Respect Instagram API limits
5. **Error Sanitization**: No sensitive data in logs

## 🚀 Deployment

### Production Checklist

- [ ] Instagram app approved for production
- [ ] Environment variables configured
- [ ] Database schema migrated
- [ ] SSL certificates installed
- [ ] Monitoring and logging configured
- [ ] Error tracking enabled

### Performance Optimization

- Background sync scheduling
- Efficient database queries
- Connection pooling
- Caching strategies
- Rate limit handling

## 📈 Future Enhancements

1. **Instagram Graph API**: Business account features
2. **Content Publishing**: Direct posting to Instagram
3. **Analytics Integration**: Detailed performance metrics
4. **Webhook Support**: Real-time updates
5. **Multi-account Management**: Bulk operations

## 🆘 Troubleshooting

### Common Issues

1. **"Instagram OAuth not configured"**
   - Check environment variables
   - Verify Instagram app settings

2. **"Token exchange failed"**
   - Verify client secret
   - Check redirect URI configuration

3. **"Account sync failed"**
   - Check access token validity
   - Verify Instagram API status

### Debug Mode

Enable detailed logging:
```bash
DEBUG=instagram:* npm run dev
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review error logs
3. Run manual tests
4. Contact development team

---

**Note**: This integration uses Instagram Basic Display API. For business features, consider upgrading to Instagram Graph API.
